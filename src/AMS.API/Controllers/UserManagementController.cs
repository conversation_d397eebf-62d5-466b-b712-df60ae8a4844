using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AMS.Core.Interfaces;
using AMS.Core.Constants;
using AMS.API.Models;
using AMS.Core.Entities;
using AMS.Core.Exceptions;

namespace AMS.API.Controllers;

/// <summary>
/// User Management controller for web UI views (Admin only)
/// </summary>
[Authorize(Policy = ApplicationConstants.Policies.RequireAdministratorRole)]
[Route("users")]
public class UserManagementController : Controller
{
    private readonly IUserService _userService;
    private readonly ILogger<UserManagementController> _logger;

    public UserManagementController(
        IUserService userService,
        ILogger<UserManagementController> logger)
    {
        _userService = userService;
        _logger = logger;
    }

    /// <summary>
    /// List all users
    /// </summary>
    [Route("")]
    [Route("index")]
    public async Task<IActionResult> Index(string? search = null, string? role = null, bool? isActive = null)
    {
        try
        {
            IEnumerable<User> users;

            if (!string.IsNullOrEmpty(search))
            {
                users = await _userService.SearchUsersAsync(search);
            }
            else if (!string.IsNullOrEmpty(role))
            {
                users = await _userService.GetUsersByRoleAsync(role);
            }
            else
            {
                users = await _userService.GetAllUsersAsync();
            }

            // Apply active filter if specified
            if (isActive.HasValue)
            {
                users = users.Where(u => u.IsActive == isActive.Value);
            }

            var model = new UserListViewModel
            {
                Users = users.ToList(),
                SearchTerm = search,
                RoleFilter = role,
                IsActiveFilter = isActive
            };

            return View(model);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading users list");
            TempData["ErrorMessage"] = "Error loading users. Please try again.";
            return View(new UserListViewModel());
        }
    }

    /// <summary>
    /// User details
    /// </summary>
    [Route("details/{id}")]
    public async Task<IActionResult> Details(Guid id)
    {
        try
        {
            var user = await _userService.GetUserByIdAsync(id);
            if (user == null)
            {
                TempData["ErrorMessage"] = "User not found.";
                return RedirectToAction(nameof(Index));
            }

            return View(user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading user details for ID: {Id}", id);
            TempData["ErrorMessage"] = "Error loading user details. Please try again.";
            return RedirectToAction(nameof(Index));
        }
    }

    /// <summary>
    /// Create user form
    /// </summary>
    [Route("create")]
    public IActionResult Create()
    {
        return View(new CreateUserViewModel());
    }

    /// <summary>
    /// Process create user form
    /// </summary>
    [HttpPost]
    [ValidateAntiForgeryToken]
    [Route("create")]
    public async Task<IActionResult> Create(CreateUserViewModel model)
    {
        if (!ModelState.IsValid)
        {
            return View(model);
        }

        try
        {
            var user = new User
            {
                FirstName = model.FirstName,
                LastName = model.LastName,
                Email = model.Email,
                Role = model.Role,
                IsActive = model.IsActive,
                PasswordHash = model.Password // Will be hashed by the service
            };

            await _userService.CreateUserAsync(user);

            TempData["SuccessMessage"] = $"User {user.FullName} created successfully.";
            return RedirectToAction(nameof(Index));
        }
        catch (DuplicateEmailException ex)
        {
            ModelState.AddModelError("Email", ex.Message);
            return View(model);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating user");
            ModelState.AddModelError(string.Empty, "An error occurred while creating the user. Please try again.");
            return View(model);
        }
    }

    /// <summary>
    /// Edit user form
    /// </summary>
    [Route("edit/{id}")]
    public async Task<IActionResult> Edit(Guid id)
    {
        try
        {
            var user = await _userService.GetUserByIdAsync(id);
            if (user == null)
            {
                TempData["ErrorMessage"] = "User not found.";
                return RedirectToAction(nameof(Index));
            }

            var model = new EditUserViewModel
            {
                Id = user.Id,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Email = user.Email,
                Role = user.Role,
                IsActive = user.IsActive
            };

            return View(model);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading user for edit, ID: {Id}", id);
            TempData["ErrorMessage"] = "Error loading user for editing. Please try again.";
            return RedirectToAction(nameof(Index));
        }
    }

    /// <summary>
    /// Process edit user form
    /// </summary>
    [HttpPost]
    [ValidateAntiForgeryToken]
    [Route("edit/{id}")]
    public async Task<IActionResult> Edit(EditUserViewModel model)
    {
        if (!ModelState.IsValid)
        {
            return View(model);
        }

        try
        {
            var user = await _userService.GetUserByIdAsync(model.Id);
            if (user == null)
            {
                TempData["ErrorMessage"] = "User not found.";
                return RedirectToAction(nameof(Index));
            }

            user.FirstName = model.FirstName;
            user.LastName = model.LastName;
            user.Email = model.Email;
            user.Role = model.Role;
            user.IsActive = model.IsActive;

            await _userService.UpdateUserAsync(user);

            TempData["SuccessMessage"] = $"User {user.FullName} updated successfully.";
            return RedirectToAction(nameof(Details), new { id = user.Id });
        }
        catch (DuplicateEmailException ex)
        {
            ModelState.AddModelError("Email", ex.Message);
            return View(model);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user ID: {Id}", model.Id);
            ModelState.AddModelError(string.Empty, "An error occurred while updating the user. Please try again.");
            return View(model);
        }
    }

    /// <summary>
    /// Delete user confirmation
    /// </summary>
    [Route("delete/{id}")]
    public async Task<IActionResult> Delete(Guid id)
    {
        try
        {
            var user = await _userService.GetUserByIdAsync(id);
            if (user == null)
            {
                TempData["ErrorMessage"] = "User not found.";
                return RedirectToAction(nameof(Index));
            }

            return View(user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading user for delete confirmation, ID: {Id}", id);
            TempData["ErrorMessage"] = "Error loading user. Please try again.";
            return RedirectToAction(nameof(Index));
        }
    }

    /// <summary>
    /// Process delete user
    /// </summary>
    [HttpPost, ActionName("Delete")]
    [ValidateAntiForgeryToken]
    [Route("delete/{id}")]
    public async Task<IActionResult> DeleteConfirmed(Guid id)
    {
        try
        {
            var user = await _userService.GetUserByIdAsync(id);
            if (user == null)
            {
                TempData["ErrorMessage"] = "User not found.";
                return RedirectToAction(nameof(Index));
            }

            // Prevent deleting the current user
            if (User.FindFirst("user_id")?.Value == id.ToString())
            {
                TempData["ErrorMessage"] = "You cannot delete your own account.";
                return RedirectToAction(nameof(Index));
            }

            await _userService.DeleteUserAsync(id);

            TempData["SuccessMessage"] = $"User {user.FullName} deleted successfully.";
            return RedirectToAction(nameof(Index));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting user ID: {Id}", id);
            TempData["ErrorMessage"] = "An error occurred while deleting the user. Please try again.";
            return RedirectToAction(nameof(Index));
        }
    }
}
