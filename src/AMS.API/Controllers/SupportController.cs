using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AMS.Core.Interfaces;
using AMS.Core.Constants;
using AMS.API.Models;
using AMS.Core.Exceptions;

namespace AMS.API.Controllers;

/// <summary>
/// Support controller for web UI views
/// </summary>
[Route("support")]
public class SupportController : Controller
{
    private readonly ISupportQuestionService _supportQuestionService;
    private readonly IUserService _userService;
    private readonly ILogger<SupportController> _logger;

    public SupportController(
        ISupportQuestionService supportQuestionService,
        IUserService userService,
        ILogger<SupportController> logger)
    {
        _supportQuestionService = supportQuestionService;
        _userService = userService;
        _logger = logger;
    }

    /// <summary>
    /// List support questions (Admin/Manager only)
    /// </summary>
    [Authorize(Policy = ApplicationConstants.Policies.RequireUserRole)]
    [Route("")]
    [Route("index")]
    public async Task<IActionResult> Index(int page = 1, int pageSize = 10, string? status = null, string? email = null)
    {
        try
        {
            var skip = (page - 1) * pageSize;
            var supportQuestions = await _supportQuestionService.GetAllAsync(
                status: status,
                email: email,
                skip: skip,
                take: pageSize);

            var totalCount = await _supportQuestionService.GetCountAsync(
                status: status,
                email: email);

            var model = new SupportQuestionListViewModel
            {
                SupportQuestions = supportQuestions.ToList(),
                CurrentPage = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize),
                HasNextPage = page * pageSize < totalCount,
                HasPreviousPage = page > 1,
                StatusFilter = status,
                EmailFilter = email
            };

            return View(model);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading support questions list");
            TempData["ErrorMessage"] = "Error loading support questions. Please try again.";
            return View(new SupportQuestionListViewModel());
        }
    }

    /// <summary>
    /// Support question details (Admin/Manager only)
    /// </summary>
    [Authorize(Policy = ApplicationConstants.Policies.RequireUserRole)]
    [Route("details/{id}")]
    public async Task<IActionResult> Details(Guid id)
    {
        try
        {
            var supportQuestion = await _supportQuestionService.GetByIdAsync(id);
            if (supportQuestion == null)
            {
                TempData["ErrorMessage"] = "Support question not found.";
                return RedirectToAction(nameof(Index));
            }

            var model = new SupportQuestionDetailsViewModel
            {
                SupportQuestion = supportQuestion
            };

            // Get available users for assignment
            if (User.IsInRole("Administrator") || User.IsInRole("Manager"))
            {
                var users = await _userService.GetActiveUsersAsync();
                model.AvailableUsers = users.ToList();
            }

            return View(model);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading support question details for ID: {Id}", id);
            TempData["ErrorMessage"] = "Error loading support question details. Please try again.";
            return RedirectToAction(nameof(Index));
        }
    }

    /// <summary>
    /// Create support question form (Public)
    /// </summary>
    [AllowAnonymous]
    [Route("create")]
    public IActionResult Create()
    {
        return View(new CreateSupportQuestionViewModel());
    }

    /// <summary>
    /// Process create support question form (Public)
    /// </summary>
    [HttpPost]
    [AllowAnonymous]
    [ValidateAntiForgeryToken]
    [Route("create")]
    public async Task<IActionResult> Create(CreateSupportQuestionViewModel model)
    {
        if (!ModelState.IsValid)
        {
            return View(model);
        }

        try
        {
            var supportQuestion = await _supportQuestionService.CreateAsync(
                model.Name,
                model.Email,
                model.Body);

            TempData["SuccessMessage"] = "Your support question has been submitted successfully. We'll get back to you soon!";
            
            return RedirectToAction("ThankYou", new { id = supportQuestion.Id });
        }
        catch (ValidationException ex)
        {
            ModelState.AddModelError(string.Empty, ex.Message);
            return View(model);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating support question");
            ModelState.AddModelError(string.Empty, "An error occurred while submitting your question. Please try again.");
            return View(model);
        }
    }

    /// <summary>
    /// Thank you page after submitting support question
    /// </summary>
    [AllowAnonymous]
    [Route("thankyou/{id}")]
    public async Task<IActionResult> ThankYou(Guid id)
    {
        try
        {
            var supportQuestion = await _supportQuestionService.GetByIdAsync(id);
            if (supportQuestion == null)
            {
                return RedirectToAction("Index", "Home");
            }

            return View(supportQuestion);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading thank you page for support question ID: {Id}", id);
            return RedirectToAction("Index", "Home");
        }
    }

    /// <summary>
    /// Update support question status (Admin/Manager only)
    /// </summary>
    [HttpPost]
    [Authorize(Policy = ApplicationConstants.Policies.RequireUserRole)]
    [ValidateAntiForgeryToken]
    [Route("updatestatus")]
    public async Task<IActionResult> UpdateStatus(Guid id, string status)
    {
        try
        {
            await _supportQuestionService.UpdateStatusAsync(id, status);
            TempData["SuccessMessage"] = "Support question status updated successfully.";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating support question status for ID: {Id}", id);
            TempData["ErrorMessage"] = "Error updating support question status. Please try again.";
        }

        return RedirectToAction(nameof(Details), new { id });
    }

    /// <summary>
    /// Assign support question to user (Admin/Manager only)
    /// </summary>
    [HttpPost]
    [Authorize(Policy = ApplicationConstants.Policies.RequireUserRole)]
    [ValidateAntiForgeryToken]
    [Route("assign")]
    public async Task<IActionResult> Assign(Guid id, Guid? assignedToUserId)
    {
        try
        {
            await _supportQuestionService.AssignToUserAsync(id, assignedToUserId);
            TempData["SuccessMessage"] = assignedToUserId.HasValue 
                ? "Support question assigned successfully." 
                : "Support question unassigned successfully.";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning support question for ID: {Id}", id);
            TempData["ErrorMessage"] = "Error assigning support question. Please try again.";
        }

        return RedirectToAction(nameof(Details), new { id });
    }
}
