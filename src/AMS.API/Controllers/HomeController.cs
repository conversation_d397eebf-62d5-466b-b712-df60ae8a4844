using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AMS.Core.Interfaces;
using AMS.Core.Constants;
using System.Diagnostics;

namespace AMS.API.Controllers;

/// <summary>
/// Home controller for web UI views
/// </summary>
public class HomeController : Controller
{
    private readonly IUserService _userService;
    private readonly ISupportQuestionService _supportQuestionService;
    private readonly ILogger<HomeController> _logger;

    public HomeController(
        IUserService userService,
        ISupportQuestionService supportQuestionService,
        ILogger<HomeController> logger)
    {
        _userService = userService;
        _supportQuestionService = supportQuestionService;
        _logger = logger;
    }

    /// <summary>
    /// Home page - public landing page
    /// </summary>
    public IActionResult Index()
    {
        return View();
    }

    /// <summary>
    /// Dashboard - authenticated users only
    /// </summary>
    [Authorize]
    public async Task<IActionResult> Dashboard()
    {
        try
        {
            var model = new DashboardViewModel();

            // Get basic statistics
            var allUsers = await _userService.GetAllUsersAsync();
            model.TotalUsers = allUsers.Count();
            model.ActiveUsers = allUsers.Count(u => u.IsActive);

            // Get support question statistics
            model.TotalSupportQuestions = await _supportQuestionService.GetCountAsync();
            model.PendingSupportQuestions = await _supportQuestionService.GetCountAsync(
                status: ApplicationConstants.SupportQuestionStatus.Pending);
            model.InProgressSupportQuestions = await _supportQuestionService.GetCountAsync(
                status: ApplicationConstants.SupportQuestionStatus.InProgress);
            model.ResolvedSupportQuestions = await _supportQuestionService.GetCountAsync(
                status: ApplicationConstants.SupportQuestionStatus.Resolved);

            // Get recent support questions for current user if they have access
            if (User.IsInRole("Administrator") || User.IsInRole("Manager"))
            {
                var recentQuestions = await _supportQuestionService.GetAllAsync(
                    skip: 0, 
                    take: 5);
                model.RecentSupportQuestions = recentQuestions.ToList();
            }
            else if (User.IsInRole("User"))
            {
                // Get questions assigned to current user
                if (Guid.TryParse(User.FindFirst("user_id")?.Value, out var userId))
                {
                    var assignedQuestions = await _supportQuestionService.GetByAssignedUserAsync(userId);
                    model.RecentSupportQuestions = assignedQuestions.Take(5).ToList();
                }
            }

            return View(model);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading dashboard");
            TempData["ErrorMessage"] = "Error loading dashboard data. Please try again.";
            return View(new DashboardViewModel());
        }
    }

    /// <summary>
    /// Privacy page
    /// </summary>
    public IActionResult Privacy()
    {
        return View();
    }

    /// <summary>
    /// Error page
    /// </summary>
    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel 
        { 
            RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier 
        });
    }
}

/// <summary>
/// View model for dashboard
/// </summary>
public class DashboardViewModel
{
    public int TotalUsers { get; set; }
    public int ActiveUsers { get; set; }
    public int TotalSupportQuestions { get; set; }
    public int PendingSupportQuestions { get; set; }
    public int InProgressSupportQuestions { get; set; }
    public int ResolvedSupportQuestions { get; set; }
    public List<AMS.Core.Entities.SupportQuestion> RecentSupportQuestions { get; set; } = new();
}

/// <summary>
/// View model for error page
/// </summary>
public class ErrorViewModel
{
    public string? RequestId { get; set; }
    public bool ShowRequestId => !string.IsNullOrEmpty(RequestId);
}
