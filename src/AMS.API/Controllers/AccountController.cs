using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using System.Security.Claims;
using AMS.Core.Interfaces;
using AMS.API.DTOs;
using AMS.API.Models;
using AMS.Core.Constants;

namespace AMS.API.Controllers;

/// <summary>
/// Account controller for web authentication views
/// </summary>
public class AccountController : Controller
{
    private readonly IAuthenticationService _authenticationService;
    private readonly IUserService _userService;
    private readonly ILogger<AccountController> _logger;

    public AccountController(
        IAuthenticationService authenticationService,
        IUserService userService,
        ILogger<AccountController> logger)
    {
        _authenticationService = authenticationService;
        _userService = userService;
        _logger = logger;
    }

    /// <summary>
    /// Login page
    /// </summary>
    [AllowAnonymous]
    public IActionResult Login(string? returnUrl = null)
    {
        if (User.Identity?.IsAuthenticated == true)
        {
            return RedirectToAction("Dashboard", "Home");
        }

        ViewData["ReturnUrl"] = returnUrl;
        return View(new LoginViewModel());
    }

    /// <summary>
    /// Process login form
    /// </summary>
    [HttpPost]
    [AllowAnonymous]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Login(LoginViewModel model, string? returnUrl = null)
    {
        ViewData["ReturnUrl"] = returnUrl;

        if (!ModelState.IsValid)
        {
            return View(model);
        }

        try
        {
            var result = await _authenticationService.AuthenticateAsync(model.Email, model.Password);

            if (!result.IsSuccess)
            {
                ModelState.AddModelError(string.Empty, result.ErrorMessage ?? "Invalid login attempt.");
                return View(model);
            }

            // Create claims for cookie authentication
            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, result.User!.Id.ToString()),
                new(ClaimTypes.Name, result.User.Email),
                new(ClaimTypes.Email, result.User.Email),
                new(ClaimTypes.GivenName, result.User.FirstName),
                new(ClaimTypes.Surname, result.User.LastName),
                new(ClaimTypes.Role, result.User.Role),
                new("user_id", result.User.Id.ToString()),
                new("email", result.User.Email),
                new("first_name", result.User.FirstName),
                new("last_name", result.User.LastName),
                new("role", result.User.Role),
                new("is_active", result.User.IsActive.ToString().ToLower())
            };

            var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
            var authProperties = new AuthenticationProperties
            {
                IsPersistent = model.RememberMe,
                ExpiresUtc = DateTimeOffset.UtcNow.AddMinutes(ApplicationConstants.Authentication.TokenExpirationMinutes)
            };

            await HttpContext.SignInAsync(
                CookieAuthenticationDefaults.AuthenticationScheme,
                new ClaimsPrincipal(claimsIdentity),
                authProperties);

            // Store JWT token in session for API calls
            HttpContext.Session.SetString("AccessToken", result.AccessToken!);
            HttpContext.Session.SetString("RefreshToken", result.RefreshToken!);

            _logger.LogInformation("User {Email} logged in successfully", model.Email);

            TempData["SuccessMessage"] = "Welcome back! You have been logged in successfully.";

            if (!string.IsNullOrEmpty(returnUrl) && Url.IsLocalUrl(returnUrl))
            {
                return Redirect(returnUrl);
            }

            return RedirectToAction("Dashboard", "Home");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during login for user {Email}", model.Email);
            ModelState.AddModelError(string.Empty, "An error occurred during login. Please try again.");
            return View(model);
        }
    }

    /// <summary>
    /// Logout
    /// </summary>
    [Authorize]
    public async Task<IActionResult> Logout()
    {
        try
        {
            // Get token from session and revoke it
            var token = HttpContext.Session.GetString("AccessToken");
            if (!string.IsNullOrEmpty(token))
            {
                await _authenticationService.RevokeTokenAsync(token);
            }

            // Clear session
            HttpContext.Session.Clear();

            // Sign out from cookie authentication
            await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);

            _logger.LogInformation("User {Email} logged out", User.Identity?.Name);

            TempData["InfoMessage"] = "You have been logged out successfully.";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during logout for user {Email}", User.Identity?.Name);
            TempData["ErrorMessage"] = "An error occurred during logout.";
        }

        return RedirectToAction("Index", "Home");
    }

    /// <summary>
    /// User profile page
    /// </summary>
    [Authorize]
    public async Task<IActionResult> Profile()
    {
        try
        {
            if (!Guid.TryParse(User.FindFirst("user_id")?.Value, out var userId))
            {
                return RedirectToAction("Login");
            }

            var user = await _userService.GetUserByIdAsync(userId);
            if (user == null)
            {
                TempData["ErrorMessage"] = "User not found.";
                return RedirectToAction("Login");
            }

            var model = new ProfileViewModel
            {
                Id = user.Id,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Email = user.Email,
                Role = user.Role,
                IsActive = user.IsActive,
                CreatedAt = user.CreatedAt,
                LastLoginAt = user.LastLoginAt
            };

            return View(model);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading profile for user {UserId}", User.FindFirst("user_id")?.Value);
            TempData["ErrorMessage"] = "Error loading profile. Please try again.";
            return RedirectToAction("Dashboard", "Home");
        }
    }

    /// <summary>
    /// Access denied page
    /// </summary>
    public IActionResult AccessDenied()
    {
        return View();
    }
}
