@{
    ViewData["Title"] = "Access Denied";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="text-center">
                <div class="access-denied-icon mb-4">
                    <i class="bi bi-shield-x text-warning" style="font-size: 5rem;"></i>
                </div>
                
                <h1 class="display-4 text-warning mb-3">Access Denied</h1>
                <h2 class="h4 mb-4">You don't have permission to access this resource</h2>
                
                <p class="text-muted mb-4">
                    The page you're trying to access requires higher privileges than your current account provides. 
                    If you believe this is an error, please contact your administrator.
                </p>
                
                <div class="alert alert-info">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-info-circle me-3 fs-5"></i>
                        <div class="text-start">
                            <strong>Current User:</strong> @User.Identity?.Name<br>
                            <strong>Role:</strong> @(User.IsIn<PERSON><PERSON>("Administrator") ? "Administrator" : User.IsIn<PERSON><PERSON>("Manager") ? "Manager" : User.IsIn<PERSON><PERSON>("User") ? "User" : "Unknown")
                        </div>
                    </div>
                </div>
                
                <div class="d-flex gap-3 justify-content-center flex-wrap">
                    <a asp-controller="Home" asp-action="Dashboard" class="btn btn-primary">
                        <i class="bi bi-speedometer2 me-2"></i>
                        Go to Dashboard
                    </a>
                    <button onclick="history.back()" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-2"></i>
                        Go Back
                    </button>
                    <a asp-controller="SupportQuestions" asp-action="Create" class="btn btn-outline-info">
                        <i class="bi bi-question-circle me-2"></i>
                        Request Access
                    </a>
                </div>
                
                <hr class="my-4">
                
                <div class="text-muted">
                    <h6>Need different permissions?</h6>
                    <p class="mb-0">
                        Contact your system administrator to request additional access rights for your account.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.access-denied-icon {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    75% {
        transform: translateX(5px);
    }
}
</style>
