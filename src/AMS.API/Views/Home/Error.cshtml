@model ErrorViewModel
@{
    ViewData["Title"] = "Error";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="text-center">
                <div class="error-icon mb-4">
                    <i class="bi bi-exclamation-triangle text-danger" style="font-size: 5rem;"></i>
                </div>
                
                <h1 class="display-4 text-danger mb-3">Oops!</h1>
                <h2 class="h4 mb-4">Something went wrong</h2>
                
                <p class="text-muted mb-4">
                    We're sorry, but an error occurred while processing your request. 
                    Please try again or contact support if the problem persists.
                </p>
                
                @if (Model.ShowRequestId)
                {
                    <div class="alert alert-info">
                        <strong>Request ID:</strong> @Model.RequestId
                        <br>
                        <small class="text-muted">Please include this ID when contacting support.</small>
                    </div>
                }
                
                <div class="d-flex gap-3 justify-content-center">
                    <a asp-controller="Home" asp-action="Index" class="btn btn-primary">
                        <i class="bi bi-house me-2"></i>
                        Go Home
                    </a>
                    <button onclick="history.back()" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-2"></i>
                        Go Back
                    </button>
                    <a asp-controller="SupportQuestions" asp-action="Create" class="btn btn-outline-info">
                        <i class="bi bi-question-circle me-2"></i>
                        Get Help
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-icon {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}
</style>
